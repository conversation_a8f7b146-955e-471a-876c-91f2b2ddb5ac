#include <iostream>
#include <vector>
#include <string>

#ifdef _WIN32
#include <windows.h>
#include <winioctl.h>
#endif

std::vector<std::string> getPhysicalDrives()
{
    std::vector<std::string> drives;
    
#ifdef _WIN32
    // Windows: 枚举物理驱动器
    for (int i = 0; i < 32; ++i) {
        std::string drivePath = "\\\\.\\PhysicalDrive" + std::to_string(i);
        HANDLE hDrive = CreateFileA(drivePath.c_str(),
                                   0, // 不需要访问权限，只是检查存在性
                                   FILE_SHARE_READ | FILE_SHARE_WRITE,
                                   NULL,
                                   OPEN_EXISTING,
                                   0,
                                   NULL);
        
        if (hDrive != INVALID_HANDLE_VALUE) {
            drives.push_back(drivePath);
            CloseHandle(hDrive);
        }
    }
#else
    // Linux/Unix: 这里只是示例
    drives.push_back("/dev/sda");
    drives.push_back("/dev/sdb");
#endif
    
    return drives;
}

std::string getDriveInfo(const std::string& drivePath)
{
    std::string info = drivePath;
    
#ifdef _WIN32
    HANDLE hDrive = CreateFileA(drivePath.c_str(),
                               GENERIC_READ,
                               FILE_SHARE_READ | FILE_SHARE_WRITE,
                               NULL,
                               OPEN_EXISTING,
                               0,
                               NULL);
    
    if (hDrive != INVALID_HANDLE_VALUE) {
        DISK_GEOMETRY_EX diskGeometry;
        DWORD bytesReturned;
        
        if (DeviceIoControl(hDrive,
                           IOCTL_DISK_GET_DRIVE_GEOMETRY_EX,
                           NULL, 0,
                           &diskGeometry, sizeof(diskGeometry),
                           &bytesReturned, NULL)) {
            
            long long sizeBytes = diskGeometry.DiskSize.QuadPart;
            double sizeGB = sizeBytes / (1024.0 * 1024.0 * 1024.0);
            
            info = drivePath + " (" + std::to_string(sizeGB) + " GB)";
        }
        
        CloseHandle(hDrive);
    }
#endif
    
    return info;
}

int main()
{
    std::cout << "检测物理磁盘..." << std::endl;
    
    auto drives = getPhysicalDrives();
    
    if (drives.empty()) {
        std::cout << "未找到物理驱动器" << std::endl;
        return 1;
    }
    
    std::cout << "找到 " << drives.size() << " 个物理驱动器:" << std::endl;
    
    for (const auto& drive : drives) {
        std::string info = getDriveInfo(drive);
        std::cout << "  " << info << std::endl;
    }
    
    return 0;
}
