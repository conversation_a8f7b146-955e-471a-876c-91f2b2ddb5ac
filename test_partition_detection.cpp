#include <iostream>
#include <vector>
#include <string>

#ifdef _WIN32
#include <windows.h>
#include <winioctl.h>
#include <fileapi.h>
#endif

std::vector<std::string> getPartitionsForDisk(int driveNumber)
{
    std::vector<std::string> partitions;
    
#ifdef _WIN32
    std::cout << "检测物理磁盘 " << driveNumber << " 的分区..." << std::endl;
    
    // 获取所有逻辑驱动器
    DWORD drives = GetLogicalDrives();
    
    for (int i = 0; i < 26; ++i) {
        if (drives & (1 << i)) {
            char driveLetter = 'A' + i;
            std::string driveRoot = std::string(1, driveLetter) + ":\\";
            
            std::cout << "检查驱动器: " << driveRoot << std::endl;
            
            // 获取驱动器类型
            UINT driveType = GetDriveTypeA(driveRoot.c_str());
            if (driveType == DRIVE_FIXED || driveType == DRIVE_REMOVABLE) {
                // 尝试获取这个驱动器对应的物理磁盘号
                std::string volumePath = "\\\\.\\" + std::string(1, driveLetter) + ":";
                HANDLE hVolume = CreateFileA(volumePath.c_str(),
                                           0,
                                           FILE_SHARE_READ | FILE_SHARE_WRITE,
                                           NULL,
                                           OPEN_EXISTING,
                                           0,
                                           NULL);
                
                if (hVolume != INVALID_HANDLE_VALUE) {
                    VOLUME_DISK_EXTENTS diskExtents;
                    DWORD bytesReturned;
                    
                    if (DeviceIoControl(hVolume,
                                       IOCTL_VOLUME_GET_VOLUME_DISK_EXTENTS,
                                       NULL, 0,
                                       &diskExtents, sizeof(diskExtents),
                                       &bytesReturned, NULL)) {
                        
                        if (diskExtents.NumberOfDiskExtents > 0) {
                            DWORD physicalDriveNumber = diskExtents.Extents[0].DiskNumber;
                            std::cout << "  -> 物理磁盘号: " << physicalDriveNumber << std::endl;
                            
                            if ((int)physicalDriveNumber == driveNumber) {
                                // 获取磁盘空间信息
                                ULARGE_INTEGER freeBytesAvailable, totalNumberOfBytes, totalNumberOfFreeBytes;
                                if (GetDiskFreeSpaceExA(driveRoot.c_str(), 
                                                       &freeBytesAvailable, 
                                                       &totalNumberOfBytes, 
                                                       &totalNumberOfFreeBytes)) {
                                    double totalGB = totalNumberOfBytes.QuadPart / (1024.0 * 1024.0 * 1024.0);
                                    std::string displayText = driveRoot + " (" + std::to_string(totalGB) + " GB)";
                                    partitions.push_back(displayText);
                                    std::cout << "  -> 匹配! 添加分区: " << displayText << std::endl;
                                } else {
                                    partitions.push_back(driveRoot);
                                    std::cout << "  -> 匹配! 添加分区: " << driveRoot << std::endl;
                                }
                            }
                        }
                    } else {
                        std::cout << "  -> 无法获取磁盘扩展信息" << std::endl;
                    }
                    CloseHandle(hVolume);
                } else {
                    std::cout << "  -> 无法打开卷: " << volumePath << std::endl;
                }
            } else {
                std::cout << "  -> 跳过 (驱动器类型: " << driveType << ")" << std::endl;
            }
        }
    }
#endif
    
    return partitions;
}

int main()
{
    std::cout << "测试分区检测功能..." << std::endl;
    
    // 测试物理磁盘0的分区
    auto partitions = getPartitionsForDisk(0);
    
    std::cout << "\n物理磁盘0的分区:" << std::endl;
    if (partitions.empty()) {
        std::cout << "  未找到分区" << std::endl;
    } else {
        for (const auto& partition : partitions) {
            std::cout << "  " << partition << std::endl;
        }
    }
    
    // 测试物理磁盘1的分区（如果存在）
    partitions = getPartitionsForDisk(1);
    
    std::cout << "\n物理磁盘1的分区:" << std::endl;
    if (partitions.empty()) {
        std::cout << "  未找到分区或磁盘不存在" << std::endl;
    } else {
        for (const auto& partition : partitions) {
            std::cout << "  " << partition << std::endl;
        }
    }
    
    return 0;
}
