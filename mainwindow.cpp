#include "mainwindow.h"
#include "filesearchworker.h"
#include <QApplication>
#include <QDir>
#include <QFileInfo>
#include <QTextStream>
#include <QMessageBox>
#include <QHeaderView>
#include <QGroupBox>
#include <QTextCodec>
#include <QStatusBar>
#include <QDateTime>
#ifdef Q_OS_WIN
#include <windows.h>
#include <winioctl.h>
#include <fileapi.h>
#endif
MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_searchWorker(nullptr)
    , m_searchThread(nullptr)
{
    setupUI();
    loadDiskList();
}

MainWindow::~MainWindow()
{
    if (m_searchThread && m_searchThread->isRunning()) {
        m_searchThread->quit();
        m_searchThread->wait();
    }
}

void MainWindow::setupUI()
{
    m_centralWidget = new QWidget;
    setCentralWidget(m_centralWidget);
    
    // 主分割器
    m_mainSplitter = new QSplitter(Qt::Horizontal);
    m_leftSplitter = new QSplitter(Qt::Vertical);
    
    // 磁盘列表
    QGroupBox *diskGroup = new QGroupBox("物理磁盘");
    QVBoxLayout *diskLayout = new QVBoxLayout;
    m_diskList = new QListWidget;
    diskLayout->addWidget(m_diskList);
    diskGroup->setLayout(diskLayout);
    
    // 分区列表
    QGroupBox *partitionGroup = new QGroupBox("逻辑分区/挂载点");
    QVBoxLayout *partitionLayout = new QVBoxLayout;
    m_partitionList = new QListWidget;
    partitionLayout->addWidget(m_partitionList);
    partitionGroup->setLayout(partitionLayout);
    
    // 搜索区域
    QGroupBox *searchGroup = new QGroupBox("搜索");
    QVBoxLayout *searchLayout = new QVBoxLayout;
    m_searchEdit = new QLineEdit;
    m_searchEdit->setPlaceholderText("输入搜索关键字...");
    m_searchButton = new QPushButton("开始搜索");
    searchLayout->addWidget(m_searchEdit);
    searchLayout->addWidget(m_searchButton);
    searchGroup->setLayout(searchLayout);
    
    // 左侧面板
    m_leftSplitter->addWidget(diskGroup);
    m_leftSplitter->addWidget(partitionGroup);
    m_leftSplitter->addWidget(searchGroup);
    m_leftSplitter->setSizes({150, 150, 100});
    
    // 结果列表
    QGroupBox *resultGroup = new QGroupBox("搜索结果");
    QVBoxLayout *resultLayout = new QVBoxLayout;
    m_resultList = new QTreeWidget;
    m_resultList->setHeaderLabels({"文件名", "路径", "大小", "修改时间"});
    m_resultList->header()->setStretchLastSection(false);
    m_resultList->header()->setSectionResizeMode(0, QHeaderView::ResizeToContents);
    m_resultList->header()->setSectionResizeMode(1, QHeaderView::Stretch);
    resultLayout->addWidget(m_resultList);
    resultGroup->setLayout(resultLayout);
    
    // 文件预览
    QGroupBox *previewGroup = new QGroupBox("文件预览");
    QVBoxLayout *previewLayout = new QVBoxLayout;
    m_previewEdit = new QTextEdit;
    m_previewEdit->setReadOnly(true);
    previewLayout->addWidget(m_previewEdit);
    previewGroup->setLayout(previewLayout);
    
    // 右侧分割器
    QSplitter *rightSplitter = new QSplitter(Qt::Vertical);
    rightSplitter->addWidget(resultGroup);
    rightSplitter->addWidget(previewGroup);
    rightSplitter->setSizes({300, 200});
    
    // 主分割器
    m_mainSplitter->addWidget(m_leftSplitter);
    m_mainSplitter->addWidget(rightSplitter);
    m_mainSplitter->setSizes({250, 550});
    
    // 状态栏
    m_statusLabel = new QLabel("就绪");
    m_progressBar = new QProgressBar;
    m_progressBar->setVisible(false);
    statusBar()->addWidget(m_statusLabel, 1);
    statusBar()->addWidget(m_progressBar);
    
    // 主布局
    QVBoxLayout *mainLayout = new QVBoxLayout;
    mainLayout->addWidget(m_mainSplitter);
    m_centralWidget->setLayout(mainLayout);
    
    // 连接信号
    connect(m_diskList, &QListWidget::itemClicked, this, &MainWindow::onDiskSelected);
    connect(m_partitionList, &QListWidget::itemClicked, this, &MainWindow::onPartitionSelected);
    connect(m_searchButton, &QPushButton::clicked, this, &MainWindow::onSearchClicked);
    connect(m_resultList, &QTreeWidget::itemClicked, this, &MainWindow::onFileSelected);
    connect(m_searchEdit, &QLineEdit::returnPressed, this, &MainWindow::onSearchClicked);
    
    setWindowTitle("文件内容快速检查工具");
    resize(800, 600);
}

void MainWindow::loadDiskList()
{
    m_diskList->clear();

    // 获取物理驱动器
    QStringList physicalDrives = getPhysicalDrives();
    for (const QString &drive : physicalDrives) {
        QString driveInfo = getDriveInfo(drive);
        QListWidgetItem *item = new QListWidgetItem(driveInfo);
        item->setData(Qt::UserRole, drive);
        m_diskList->addItem(item);
    }

    // 如果没有找到物理驱动器，回退到逻辑卷
    if (physicalDrives.isEmpty()) {
        QList<QStorageInfo> storages = QStorageInfo::mountedVolumes();
        for (const QStorageInfo &storage : storages) {
            if (storage.isValid() && storage.isReady()) {
                QString displayText = QString("%1 (%2)")
                    .arg(storage.rootPath())
                    .arg(storage.displayName());
                QListWidgetItem *item = new QListWidgetItem(displayText);
                item->setData(Qt::UserRole, storage.rootPath());
                m_diskList->addItem(item);
            }
        }
    }
}

void MainWindow::loadPartitionList(const QString &diskPath)
{
    m_partitionList->clear();

    // 如果是物理驱动器路径，获取其逻辑分区
    if (diskPath.startsWith("\\\\.\\PhysicalDrive")) {
        QStringList partitions = getPartitionsForDisk(diskPath);
        if (partitions.isEmpty()) {
            QListWidgetItem *item = new QListWidgetItem("未找到分区或无访问权限");
            item->setFlags(item->flags() & ~Qt::ItemIsSelectable);
            m_partitionList->addItem(item);
        } else {
            for (const QString &partition : partitions) {
                QListWidgetItem *item = new QListWidgetItem(partition);
                // 提取驱动器字母作为搜索路径
                QString driveLetter = partition.left(2); // 例如 "C:"
                if (driveLetter.endsWith(":")) {
                    driveLetter += "\\"; // 变成 "C:\"
                }
                item->setData(Qt::UserRole, driveLetter);
                m_partitionList->addItem(item);
            }
        }
    } else if (diskPath.startsWith("/dev/")) {
        // Linux: 获取设备的分区
        QString deviceName = diskPath.mid(5); // 移除 "/dev/"
        QDir devDir("/dev");
        QStringList filters;
        filters << deviceName + "[0-9]*";
        devDir.setNameFilters(filters);
        QStringList partitions = devDir.entryList(QDir::System);

        for (const QString &partition : partitions) {
            QString partPath = "/dev/" + partition;
            QListWidgetItem *item = new QListWidgetItem(partition);
            item->setData(Qt::UserRole, partPath);
            m_partitionList->addItem(item);
        }
    } else {
        // 普通目录，列出子目录
        QDir dir(diskPath);
        QStringList subdirs = dir.entryList(QDir::Dirs | QDir::NoDotAndDotDot);
        for (const QString &subdir : subdirs) {
            QString fullPath = dir.absoluteFilePath(subdir);
            QListWidgetItem *item = new QListWidgetItem(subdir);
            item->setData(Qt::UserRole, fullPath);
            m_partitionList->addItem(item);
        }
    }
}

void MainWindow::onDiskSelected()
{
    QListWidgetItem *item = m_diskList->currentItem();
    if (item) {
        QString diskPath = item->data(Qt::UserRole).toString();
        loadPartitionList(diskPath);
        m_currentSearchPath = diskPath;
    }
}

void MainWindow::onPartitionSelected()
{
    QListWidgetItem *item = m_partitionList->currentItem();
    if (item) {
        m_currentSearchPath = item->data(Qt::UserRole).toString();
    }
}

void MainWindow::onSearchClicked()
{
    QString keyword = m_searchEdit->text().trimmed();
    if (keyword.isEmpty()) {
        QMessageBox::warning(this, "警告", "请输入搜索关键字");
        return;
    }
    
    if (m_currentSearchPath.isEmpty()) {
        QMessageBox::warning(this, "警告", "请选择搜索路径");
        return;
    }
    
    // 停止之前的搜索
    if (m_searchThread && m_searchThread->isRunning()) {
        m_searchThread->quit();
        m_searchThread->wait();
    }
    
    // 清空结果
    m_resultList->clear();
    m_previewEdit->clear();
    
    // 创建搜索线程
    m_searchThread = new QThread;
    m_searchWorker = new FileSearchWorker(m_currentSearchPath, keyword);
    m_searchWorker->moveToThread(m_searchThread);
    
    connect(m_searchThread, &QThread::started, m_searchWorker, &FileSearchWorker::search);
    connect(m_searchWorker, &FileSearchWorker::finished, this, &MainWindow::onSearchFinished);
    connect(m_searchWorker, &FileSearchWorker::progress, this, &MainWindow::onSearchProgress);
    connect(m_searchWorker, &FileSearchWorker::finished, m_searchThread, &QThread::quit);
    connect(m_searchThread, &QThread::finished, m_searchWorker, &FileSearchWorker::deleteLater);
    
    // 开始搜索
    m_searchTimer.start();
    m_progressBar->setVisible(true);
    m_progressBar->setRange(0, 0);
    m_statusLabel->setText("搜索中...");
    m_searchButton->setEnabled(false);
    
    m_searchThread->start();
}

void MainWindow::onSearchFinished(const QStringList &results, qint64 elapsedMs)
{
    m_progressBar->setVisible(false);
    m_searchButton->setEnabled(true);
    
    // 显示结果
    for (const QString &filePath : results) {
        QFileInfo fileInfo(filePath);
        QTreeWidgetItem *item = new QTreeWidgetItem;
        item->setText(0, fileInfo.fileName());
        item->setText(1, fileInfo.absolutePath());
        item->setText(2, QString::number(fileInfo.size()));
        item->setText(3, fileInfo.lastModified().toString("yyyy-MM-dd hh:mm:ss"));
        item->setData(0, Qt::UserRole, filePath);
        m_resultList->addTopLevelItem(item);
    }
    
    m_statusLabel->setText(QString("搜索完成，找到 %1 个文件，耗时 %2 毫秒")
                          .arg(results.size()).arg(elapsedMs));
}

void MainWindow::onSearchProgress(const QString &currentFile)
{
    m_statusLabel->setText(QString("正在搜索: %1").arg(currentFile));
}

void MainWindow::onFileSelected()
{
    QTreeWidgetItem *item = m_resultList->currentItem();
    if (item) {
        QString filePath = item->data(0, Qt::UserRole).toString();
        previewFile(filePath);
    }
}

void MainWindow::previewFile(const QString &filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        m_previewEdit->setText("无法打开文件");
        return;
    }

    // 读取文件开头用于编码检测
    QByteArray data = file.read(1024);
    file.seek(0);

    QString encoding = FileSearchWorker::detectEncoding(data);

    QTextStream in(&file);
    // 设置检测到的编码
    in.setCodec(encoding.toLocal8Bit());
    QString content = in.read(10000); // 只读取前10KB
    m_previewEdit->setText(content);

    if (file.size() > 10000) {
        m_previewEdit->append(QString("\n\n[文件过大，仅显示前10KB内容] [编码: %1]").arg(encoding));
    } else {
        m_previewEdit->append(QString("\n\n[编码: %1]").arg(encoding));
    }
}



QStringList MainWindow::getPhysicalDrives()
{
    QStringList drives;

#ifdef Q_OS_WIN
    // Windows: 枚举物理驱动器
    for (int i = 0; i < 32; ++i) {
        QString drivePath = QString("\\\\.\\PhysicalDrive%1").arg(i);
        HANDLE hDrive = CreateFileA(drivePath.toLocal8Bit().constData(),
                                   0, // 不需要访问权限，只是检查存在性
                                   FILE_SHARE_READ | FILE_SHARE_WRITE,
                                   NULL,
                                   OPEN_EXISTING,
                                   0,
                                   NULL);

        if (hDrive != INVALID_HANDLE_VALUE) {
            drives.append(drivePath);
            CloseHandle(hDrive);
        }
    }
#else
    // Linux/Unix: 枚举 /dev/sd* 和 /dev/nvme*
    QDir devDir("/dev");
    QStringList filters;
    filters << "sd[a-z]" << "nvme[0-9]n[0-9]" << "hd[a-z]";
    devDir.setNameFilters(filters);
    QStringList deviceFiles = devDir.entryList(QDir::System);

    for (const QString &device : deviceFiles) {
        drives.append("/dev/" + device);
    }
#endif

    return drives;
}

QString MainWindow::getDriveInfo(const QString &drivePath)
{
    QString info = drivePath;

#ifdef Q_OS_WIN
    HANDLE hDrive = CreateFileA(drivePath.toLocal8Bit().constData(),
                               GENERIC_READ,
                               FILE_SHARE_READ | FILE_SHARE_WRITE,
                               NULL,
                               OPEN_EXISTING,
                               0,
                               NULL);

    if (hDrive != INVALID_HANDLE_VALUE) {
        DISK_GEOMETRY_EX diskGeometry;
        DWORD bytesReturned;

        if (DeviceIoControl(hDrive,
                           IOCTL_DISK_GET_DRIVE_GEOMETRY_EX,
                           NULL, 0,
                           &diskGeometry, sizeof(diskGeometry),
                           &bytesReturned, NULL)) {

            qint64 sizeBytes = diskGeometry.DiskSize.QuadPart;
            double sizeGB = sizeBytes / (1024.0 * 1024.0 * 1024.0);

            info = QString("%1 (%.1f GB)").arg(drivePath).arg(sizeGB);
        }

        CloseHandle(hDrive);
    }
#else
    // Linux/Unix: 尝试获取设备大小
    QFile deviceFile(drivePath);
    if (deviceFile.exists()) {
        QFileInfo fileInfo(drivePath);
        info = QString("%1 (%2)").arg(drivePath).arg(fileInfo.baseName());
    }
#endif

    return info;
}

int MainWindow::getPhysicalDriveNumber(const QString &diskPath)
{
    // 从 "\\.\PhysicalDrive0" 中提取数字
    if (diskPath.startsWith("\\\\.\\PhysicalDrive")) {
        QString numberStr = diskPath.mid(17); // 移除 "\\.\PhysicalDrive"
        bool ok;
        int number = numberStr.toInt(&ok);
        return ok ? number : -1;
    }
    return -1;
}

QStringList MainWindow::getPartitionsForDisk(const QString &diskPath)
{
    QStringList partitions;

#ifdef Q_OS_WIN
    int driveNumber = getPhysicalDriveNumber(diskPath);
    if (driveNumber < 0) {
        return partitions;
    }

    // 获取所有逻辑驱动器
    DWORD drives = GetLogicalDrives();

    for (int i = 0; i < 26; ++i) {
        if (drives & (1 << i)) {
            char driveLetter = 'A' + i;
            QString driveRoot = QString("%1:\\").arg(driveLetter);

            // 获取驱动器类型
            UINT driveType = GetDriveTypeA(driveRoot.toLocal8Bit().constData());
            if (driveType == DRIVE_FIXED || driveType == DRIVE_REMOVABLE) {
                // 尝试获取这个驱动器对应的物理磁盘号
                QString volumePath = QString("\\\\.\\%1:").arg(driveLetter);
                HANDLE hVolume = CreateFileA(volumePath.toLocal8Bit().constData(),
                                           0,
                                           FILE_SHARE_READ | FILE_SHARE_WRITE,
                                           NULL,
                                           OPEN_EXISTING,
                                           0,
                                           NULL);

                if (hVolume != INVALID_HANDLE_VALUE) {
                    VOLUME_DISK_EXTENTS diskExtents;
                    DWORD bytesReturned;

                    if (DeviceIoControl(hVolume,
                                       IOCTL_VOLUME_GET_VOLUME_DISK_EXTENTS,
                                       NULL, 0,
                                       &diskExtents, sizeof(diskExtents),
                                       &bytesReturned, NULL)) {

                        if (diskExtents.NumberOfDiskExtents > 0) {
                            DWORD physicalDriveNumber = diskExtents.Extents[0].DiskNumber;
                            if ((int)physicalDriveNumber == driveNumber) {
                                // 获取卷信息
                                QStorageInfo storage(driveRoot);
                                if (storage.isValid() && storage.isReady()) {
                                    QString displayText = QString("%1 (%2) - %.1f GB")
                                        .arg(driveRoot)
                                        .arg(storage.displayName().isEmpty() ? "本地磁盘" : storage.displayName())
                                        .arg(storage.bytesTotal() / (1024.0 * 1024.0 * 1024.0));
                                    partitions.append(displayText);
                                }
                            }
                        }
                    }
                    CloseHandle(hVolume);
                }
            }
        }
    }
#endif

    return partitions;
}
