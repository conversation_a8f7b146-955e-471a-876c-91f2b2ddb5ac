# 文件搜索工具改进说明

## UTF编码支持改进

### 1. 主程序UTF-8支持
- 在 `main.cpp` 中设置了UTF-8编码支持
- 使用 `QTextCodec::setCodecForLocale(QTextCodec::codecForName("UTF-8"))`

### 2. 智能编码检测
- 实现了 `FileSearchWorker::detectEncoding()` 静态函数
- 支持检测以下编码：
  - UTF-8 (包括BOM检测)
  - UTF-16 (包括BOM检测)
  - GBK (中文编码)
  - 系统默认编码

### 3. 文件搜索编码支持
- 在搜索文件内容时自动检测文件编码
- 根据检测结果设置正确的编码进行文本读取
- 支持更多文件类型，包括各种编程语言文件

### 4. 文件预览编码支持
- 文件预览时也使用相同的编码检测机制
- 在预览窗口底部显示检测到的编码信息

### 5. 支持的文件类型扩展
现在支持以下文件类型的UTF-8内容搜索：
- 文本文件：txt, log, ini, cfg, conf, md, rtf
- 标记语言：xml, html, htm, css, yaml, yml, toml
- 数据格式：json, csv, sql, properties
- 编程语言：
  - Web: js, ts, php, css, html
  - 系统: cpp, c, h, hpp, java, cs, py, go, rs, swift, kt, scala
  - 脚本: sh, bat, ps1, vbs, reg
- 配置文件：gitignore, dockerfile
- 文档格式：tex, latex

## 物理磁盘检测改进

### 1. Windows物理驱动器检测
- 使用Windows API枚举物理驱动器 (`\\.\PhysicalDrive0`, `\\.\PhysicalDrive1`, 等)
- 通过 `CreateFile` 和 `DeviceIoControl` 获取磁盘信息
- 显示磁盘大小信息

### 2. Linux/Unix支持
- 检测 `/dev/sd*`, `/dev/nvme*`, `/dev/hd*` 设备
- 支持现代NVMe和传统SATA/IDE设备

### 3. 分区检测改进
- 对于物理驱动器，自动列出其逻辑分区
- Windows: 显示挂载的卷和分区信息
- Linux: 检测设备的分区 (如 sda1, sda2)

### 4. 界面改进
- 磁盘列表标题改为"物理磁盘"
- 分区列表标题改为"逻辑分区/挂载点"
- 显示磁盘大小和分区信息

### 5. 兼容性保证
- 如果无法检测物理驱动器，自动回退到逻辑卷检测
- 保持与原有功能的兼容性

## 技术实现细节

### 编码检测算法
1. 首先检测BOM (Byte Order Mark)
2. 尝试UTF-8解码，检查是否有无效字符
3. 尝试GBK解码（针对中文环境）
4. 回退到系统默认编码

### 物理磁盘检测流程
1. Windows: 枚举 `\\.\PhysicalDrive0` 到 `\\.\PhysicalDrive31`
2. 使用Windows API验证驱动器存在性
3. 获取磁盘几何信息和大小
4. Linux: 扫描 `/dev` 目录下的块设备

### 错误处理
- 文件访问权限不足时的优雅降级
- 编码检测失败时的默认处理
- 物理驱动器访问失败时的回退机制

## 使用说明

1. **选择物理磁盘**: 从左侧"物理磁盘"列表中选择要搜索的磁盘
2. **选择分区**: 从"逻辑分区/挂载点"列表中选择具体的分区或挂载点
3. **输入搜索关键字**: 支持UTF-8编码的中文、英文等多语言搜索
4. **查看结果**: 搜索结果会显示文件路径、大小、修改时间
5. **预览文件**: 点击搜索结果可预览文件内容，自动检测并显示编码

## 测试建议

1. 创建包含中文、日文、韩文等多语言内容的测试文件
2. 测试不同编码格式的文件搜索
3. 验证物理磁盘检测功能
4. 测试大文件的编码检测性能
