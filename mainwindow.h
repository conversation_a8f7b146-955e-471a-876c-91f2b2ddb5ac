#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QSplitter>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QListWidget>
#include <QTreeWidget>
#include <QLineEdit>
#include <QPushButton>
#include <QTextEdit>
#include <QLabel>
#include <QProgressBar>
#include <QElapsedTimer>
#include <QThread>
#include <QFileSystemWatcher>
#include <QStorageInfo>
#ifdef Q_OS_WIN
#include <windows.h>
#endif

class FileSearchWorker;

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void onDiskSelected();
    void onPartitionSelected();
    void onSearchClicked();
    void onFileSelected();
    void onSearchFinished(const QStringList &results, qint64 elapsedMs);
    void onSearchProgress(const QString &currentFile);

private:
    void setupUI();
    void loadDiskList();
    void loadPartitionList(const QString &diskPath);
    void previewFile(const QString &filePath);
    QStringList getPhysicalDrives();
    QString getDriveInfo(const QString &drivePath);
    QStringList getPartitionsForDisk(const QString &diskPath);
    int getPhysicalDriveNumber(const QString &diskPath);

    // UI Components
    QWidget *m_centralWidget;
    QSplitter *m_mainSplitter;
    QSplitter *m_leftSplitter;
    
    QListWidget *m_diskList;
    QListWidget *m_partitionList;
    QLineEdit *m_searchEdit;
    QPushButton *m_searchButton;
    QTreeWidget *m_resultList;
    QTextEdit *m_previewEdit;
    QLabel *m_statusLabel;
    QProgressBar *m_progressBar;
    
    // Search functionality
    FileSearchWorker *m_searchWorker;
    QThread *m_searchThread;
    QElapsedTimer m_searchTimer;
    QString m_currentSearchPath;
};

#endif // MAINWINDOW_H