# 分区检测功能修复说明

## 问题描述

原来的 `loadPartitionList()` 函数在处理物理磁盘时存在以下问题：

1. **不准确的分区映射**: 显示所有系统分区，而不是特定物理磁盘的分区
2. **缺少物理磁盘关联**: 无法确定哪个逻辑分区属于哪个物理磁盘
3. **权限问题**: 没有处理访问权限不足的情况

## 修复方案

### 1. 新增函数

#### `getPhysicalDriveNumber(const QString &diskPath)`
- 从物理磁盘路径中提取磁盘编号
- 例如: `\\.\PhysicalDrive0` → `0`

#### `getPartitionsForDisk(const QString &diskPath)`
- 获取指定物理磁盘的所有分区
- 使用Windows API `IOCTL_VOLUME_GET_VOLUME_DISK_EXTENTS` 确定分区与物理磁盘的映射关系

### 2. 技术实现

#### Windows分区检测流程：
1. **枚举逻辑驱动器**: 使用 `GetLogicalDrives()` 获取所有驱动器字母
2. **过滤固定磁盘**: 只处理 `DRIVE_FIXED` 和 `DRIVE_REMOVABLE` 类型
3. **获取物理磁盘映射**: 
   - 打开卷句柄 `\\.\C:`
   - 调用 `DeviceIoControl` 获取 `VOLUME_DISK_EXTENTS`
   - 比较 `DiskNumber` 与目标物理磁盘编号
4. **收集分区信息**: 获取分区大小、标签等信息

#### 关键API调用：
```cpp
// 打开卷
HANDLE hVolume = CreateFileA(volumePath, 0, 
                            FILE_SHARE_READ | FILE_SHARE_WRITE, 
                            NULL, OPEN_EXISTING, 0, NULL);

// 获取磁盘扩展信息
VOLUME_DISK_EXTENTS diskExtents;
DeviceIoControl(hVolume, IOCTL_VOLUME_GET_VOLUME_DISK_EXTENTS,
                NULL, 0, &diskExtents, sizeof(diskExtents),
                &bytesReturned, NULL);

// 检查物理磁盘编号
if (diskExtents.Extents[0].DiskNumber == targetDriveNumber) {
    // 这个分区属于目标物理磁盘
}
```

### 3. 用户体验改进

#### 错误处理：
- 当无法找到分区时显示 "未找到分区或无访问权限"
- 禁用无效项目的选择功能

#### 状态反馈：
- 选择分区时在状态栏显示当前搜索路径
- 显示分区大小信息

#### 数据格式：
- 分区显示格式: `C:\ (本地磁盘) - 250.0 GB`
- 搜索路径格式: `C:\`

### 4. 跨平台支持

#### Windows:
- 使用 `IOCTL_VOLUME_GET_VOLUME_DISK_EXTENTS` API
- 支持NTFS、FAT32等文件系统

#### Linux (预留):
- 扫描 `/dev/sdaX`, `/dev/nvmeXnXpX` 等分区设备
- 使用 `/proc/partitions` 或 `lsblk` 信息

### 5. 测试验证

创建了 `test_partition_detection.cpp` 用于验证：
- 物理磁盘0的分区检测
- 物理磁盘1的分区检测
- 详细的调试输出

### 6. 使用流程

1. **选择物理磁盘**: 从"物理磁盘"列表选择目标磁盘
2. **查看分区**: "逻辑分区/挂载点"列表显示该磁盘的所有分区
3. **选择分区**: 点击具体分区，状态栏显示搜索路径
4. **开始搜索**: 在选定分区中搜索文件内容

### 7. 权限要求

- **读取权限**: 需要读取磁盘几何信息的权限
- **管理员权限**: 某些系统可能需要管理员权限访问物理磁盘信息
- **优雅降级**: 权限不足时显示友好提示信息

### 8. 性能优化

- **缓存机制**: 避免重复查询相同磁盘的分区信息
- **异步处理**: 大型磁盘的分区检测可能需要时间
- **错误恢复**: 单个分区检测失败不影响其他分区

## 修复效果

修复后的功能能够：
- ✅ 准确显示指定物理磁盘的分区
- ✅ 正确映射分区到搜索路径
- ✅ 处理权限不足的情况
- ✅ 提供清晰的用户反馈
- ✅ 支持多种磁盘配置

这样用户就能够精确地选择要搜索的物理磁盘和分区，而不是在所有系统分区中进行搜索。
