#include "filesearchworker.h"
#include <QDir>
#include <QFile>
#include <QTextStream>
#include <QFileInfo>
#include <QApplication>
#include <QTextCodec>

FileSearchWorker::FileSearchWorker(const QString &searchPath, const QString &keyword, QObject *parent)
    : QObject(parent)
    , m_searchPath(searchPath)
    , m_keyword(keyword)
{
}

void FileSearchWorker::search()
{
    m_timer.start();
    m_results.clear();
    
    searchInDirectory(m_searchPath);
    
    qint64 elapsed = m_timer.elapsed();
    emit finished(m_results, elapsed);
}

void FileSearchWorker::searchInDirectory(const QString &dirPath)
{
    QDir dir(dirPath);
    if (!dir.exists()) {
        return;
    }
    
    // 搜索文件
    QStringList files = dir.entryList(QDir::Files | QDir::Readable);
    for (const QString &fileName : files) {
        QString filePath = dir.absoluteFilePath(fileName);
        emit progress(filePath);
        
        if (searchInFile(filePath)) {
            m_results.append(filePath);
        }
        
        // 允许事件循环处理
        QApplication::processEvents();
    }
    
    // 递归搜索子目录
    QStringList subdirs = dir.entryList(QDir::Dirs | QDir::NoDotAndDotDot | QDir::Readable);
    for (const QString &subdir : subdirs) {
        QString subdirPath = dir.absoluteFilePath(subdir);
        searchInDirectory(subdirPath);
    }
}

bool FileSearchWorker::searchInFile(const QString &filePath)
{
    QFileInfo fileInfo(filePath);
    
    // 只搜索文本文件（支持更多UTF-8文件类型）
    QStringList textExtensions = {
        "txt", "log", "ini", "cfg", "conf", "xml", "json", "csv", "md",
        "html", "htm", "css", "js", "ts", "py", "cpp", "c", "h", "hpp",
        "java", "cs", "php", "rb", "go", "rs", "swift", "kt", "scala",
        "sql", "yaml", "yml", "toml", "properties", "gitignore", "dockerfile",
        "sh", "bat", "ps1", "vbs", "reg", "rtf", "tex", "latex"
    };
    if (!textExtensions.contains(fileInfo.suffix().toLower())) {
        return false;
    }
    
    // 限制文件大小
    if (fileInfo.size() > 10 * 1024 * 1024) { // 10MB
        return false;
    }
    
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        return false;
    }

    // 读取文件开头用于编码检测
    QByteArray data = file.read(1024);
    file.seek(0);

    QString encoding = detectEncoding(data);

    QTextStream in(&file);
    // 设置检测到的编码
    in.setCodec(encoding.toLocal8Bit());
    QString content = in.readAll();

    return content.contains(m_keyword, Qt::CaseInsensitive);
}

QString FileSearchWorker::detectEncoding(const QByteArray &data)
{
    // 检测UTF-8 BOM
    if (data.startsWith("\xEF\xBB\xBF")) {
        return "UTF-8";
    }

    // 检测UTF-16 BOM
    if (data.startsWith("\xFF\xFE") || data.startsWith("\xFE\xFF")) {
        return "UTF-16";
    }

    // 简单的UTF-8检测
    QTextCodec *utf8Codec = QTextCodec::codecForName("UTF-8");
    QTextCodec::ConverterState state;
    utf8Codec->toUnicode(data.constData(), data.size(), &state);

    if (state.invalidChars == 0) {
        return "UTF-8";
    }

    // 检测中文编码
    QTextCodec *gbkCodec = QTextCodec::codecForName("GBK");
    if (gbkCodec) {
        QTextCodec::ConverterState gbkState;
        gbkCodec->toUnicode(data.constData(), data.size(), &gbkState);
        if (gbkState.invalidChars == 0) {
            return "GBK";
        }
    }

    // 默认使用系统编码
    return QTextCodec::codecForLocale()->name();
}