#include "filesearchworker.h"
#include <QDir>
#include <QFile>
#include <QTextStream>
#include <QFileInfo>
#include <QApplication>

FileSearchWorker::FileSearchWorker(const QString &searchPath, const QString &keyword, QObject *parent)
    : QObject(parent)
    , m_searchPath(searchPath)
    , m_keyword(keyword)
{
}

void FileSearchWorker::search()
{
    m_timer.start();
    m_results.clear();
    
    searchInDirectory(m_searchPath);
    
    qint64 elapsed = m_timer.elapsed();
    emit finished(m_results, elapsed);
}

void FileSearchWorker::searchInDirectory(const QString &dirPath)
{
    QDir dir(dirPath);
    if (!dir.exists()) {
        return;
    }
    
    // 搜索文件
    QStringList files = dir.entryList(QDir::Files | QDir::Readable);
    for (const QString &fileName : files) {
        QString filePath = dir.absoluteFilePath(fileName);
        emit progress(filePath);
        
        if (searchInFile(filePath)) {
            m_results.append(filePath);
        }
        
        // 允许事件循环处理
        QApplication::processEvents();
    }
    
    // 递归搜索子目录
    QStringList subdirs = dir.entryList(QDir::Dirs | QDir::NoDotAndDotDot | QDir::Readable);
    for (const QString &subdir : subdirs) {
        QString subdirPath = dir.absoluteFilePath(subdir);
        searchInDirectory(subdirPath);
    }
}

bool FileSearchWorker::searchInFile(const QString &filePath)
{
    QFileInfo fileInfo(filePath);
    
    // 只搜索文本文件
    QStringList textExtensions = {"txt", "log", "ini", "cfg", "conf", "xml", "json", "csv", "md"};
    if (!textExtensions.contains(fileInfo.suffix().toLower())) {
        return false;
    }
    
    // 限制文件大小
    if (fileInfo.size() > 10 * 1024 * 1024) { // 10MB
        return false;
    }
    
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        return false;
    }
    
    QTextStream in(&file);
    QString content = in.readAll();
    
    return content.contains(m_keyword, Qt::CaseInsensitive);
}