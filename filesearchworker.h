#ifndef FILESEARCHWORKER_H
#define FILESEARCHWORKER_H

#include <QObject>
#include <QStringList>
#include <QElapsedTimer>

class FileSearchWorker : public QObject
{
    Q_OBJECT

public:
    explicit FileSearchWorker(const QString &searchPath, const QString &keyword, QObject *parent = nullptr);

public slots:
    void search();

signals:
    void finished(const QStringList &results, qint64 elapsedMs);
    void progress(const QString &currentFile);

private:
    void searchInDirectory(const QString &dirPath);
    bool searchInFile(const QString &filePath);

public:
    static QString detectEncoding(const QByteArray &data);

    QString m_searchPath;
    QString m_keyword;
    QStringList m_results;
    QElapsedTimer m_timer;
};

#endif // FILESEARCHWORKER_H